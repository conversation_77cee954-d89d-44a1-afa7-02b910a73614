<#
.SYNOPSIS
    管理 Uvicorn 应用程序的启动和停止。

.DESCRIPTION
    这个脚本允许用户启动或停止一个特定的 Uvicorn 应用程序实例。
    它通过进程名和命令行参数来查找 Uvicorn 进程，或者通过监听的端口。

.PARAMETER Action
    指定要执行的操作：'start' 或 'stop'。

.EXAMPLE
    .\manage-uvicorn.ps1 -Action start
    启动 Uvicorn 应用程序。

.EXAMPLE
    .\manage-uvicorn.ps1 -Action stop
    停止正在运行的 Uvicorn 应用程序。

.NOTES
    - 确保 'uvicorn' 命令在您的系统 PATH 中可执行。
    - 如果您的 Uvicorn 应用程序文件不是 'app.main:app'，请修改 $UvicornApp 变量。
    - 如果您的端口不是 8000，请修改 $UvicornPort 变量。
    - 脚本默认查找 'python' 进程并筛选命令行，因为 uvicorn 通常作为 python 进程运行。
#>
param (
    [Parameter(Mandatory=$true)]
    [ValidateSet('start', 'stop')]
    [string]$Action
)

# --- 配置参数 ---
$UvicornApp = "app.main:app"
$UvicornHost = "0.0.0.0"
$UvicornPort = 8000
$UvicornArgs = "$UvicornApp --host $UvicornHost --port $UvicornPort --reload"
$UvicornExecutable = "uvicorn" # 如果uvicorn不在PATH中，请提供完整路径，例如 "C:\Python39\Scripts\uvicorn.exe"

# --- 辅助函数：查找 Uvicorn 进程 ---
function Get-UvicornProcess {
    Write-Verbose "正在查找 Uvicorn 进程..."
    # 尝试通过端口查找
    $process = Get-NetTCPConnection -LocalPort $UvicornPort -ErrorAction SilentlyContinue |
               Select-Object -ExpandProperty OwningProcess |
               Get-Process -ErrorAction SilentlyContinue |
               Where-Object { $_.CommandLine -like "*$UvicornApp*" -or $_.ProcessName -eq "uvicorn" } # 增加对uvicorn进程名的判断

    if ($process) {
        Write-Verbose "通过端口 $UvicornPort 找到 Uvicorn 进程 (PID: $($process.Id))。"
        return $process
    }

    # 如果通过端口没找到，尝试通过进程名和命令行查找
    $process = Get-Process -Name "python", "uvicorn" -ErrorAction SilentlyContinue |
               Where-Object { $_.CommandLine -like "*$UvicornApp*" -and $_.CommandLine -like "*--port $UvicornPort*" }

    if ($process) {
        Write-Verbose "通过进程名和命令行找到 Uvicorn 进程 (PID: $($process.Id))。"
        return $process
    }

    Write-Verbose "未找到 Uvicorn 进程。"
    return $null
}

# --- 主逻辑 ---
Write-Host "`n--- Uvicorn 服务管理 ---`n"

switch ($Action) {
    "start" {
        Write-Host "尝试启动 Uvicorn 应用程序..."
        $existingProcess = Get-UvicornProcess

        if ($existingProcess) {
            Write-Warning "Uvicorn 应用程序似乎已经在运行 (PID: $($existingProcess.Id))。请先停止它，或者检查是否是其他实例。"
        } else {
            try {
                Write-Host "执行命令: $UvicornExecutable $UvicornArgs"
                Start-Process -FilePath $UvicornExecutable -ArgumentList $UvicornArgs -NoNewWindow -ErrorAction Stop
                Start-Sleep -Seconds 3 # 稍作等待，让服务有时间启动并监听端口

                $newProcess = Get-UvicornProcess
                if ($newProcess) {
                    Write-Host "✅ Uvicorn 应用程序已成功启动 (PID: $($newProcess.Id))，监听端口 $UvicornPort。" -ForegroundColor Green
                } else {
                    Write-Error "Uvicorn 应用程序似乎已启动，但未能检测到其进程或端口。请手动检查。"
                }
            } catch {
                Write-Error "启动 Uvicorn 应用程序时发生错误: $($_.Exception.Message)"
                Write-Host "请确保 '$UvicornExecutable' 在您的 PATH 中，或者提供完整的路径。"
            }
        }
    }
    "stop" {
        Write-Host "尝试停止 Uvicorn 应用程序..."
        $existingProcess = Get-UvicornProcess

        if ($existingProcess) {
            try {
                Write-Host "正在停止 Uvicorn 进程 (PID: $($existingProcess.Id))..."
                Stop-Process -Id $existingProcess.Id -Force -ErrorAction Stop
                Start-Sleep -Seconds 2 # 稍作等待，确保进程完全终止
                if (-not (Get-UvicornProcess)) {
                    Write-Host "✅ Uvicorn 应用程序已成功停止。" -ForegroundColor Green
                } else {
                    Write-Warning "Uvicorn 进程 (PID: $($existingProcess.Id)) 仍在运行。可能需要手动终止。"
                }
            } catch {
                Write-Error "停止 Uvicorn 应用程序时发生错误: $($_.Exception.Message)"
            }
        } else {
            Write-Host "Uvicorn 应用程序未运行或未找到相关进程。" -ForegroundColor Yellow
        }
    }
}

Write-Host "`n--- 操作完成 ---`n"
